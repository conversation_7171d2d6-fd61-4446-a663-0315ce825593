#include "BaseDevice.h"
#include <iostream>

BaseDevice::BaseDevice(const std::string& deviceName) 
    : m_deviceName(deviceName), m_configManager(ConfigManager::getInstance()) {
    std::cout << "Device created: " << m_deviceName << std::endl;
}

bool BaseDevice::hasParameter(const std::string& paramName) const {
    return m_configManager.hasParameter(m_deviceName, paramName);
}

void BaseDevice::removeParameter(const std::string& paramName) {
    m_configManager.removeParameter(m_deviceName, paramName);
}

std::unordered_map<std::string, std::string> BaseDevice::getAllParameters() const {
    return m_configManager.getDeviceParameters(m_deviceName);
}
