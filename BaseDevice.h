#pragma once

#include "ConfigManager.h"
#include <string>
#include <memory>

/**
 * 设备基类
 * 提供统一的参数管理接口
 */
class BaseDevice {
public:
    explicit BaseDevice(const std::string& deviceName);
    virtual ~BaseDevice() = default;
    
    // 获取设备名称
    const std::string& getName() const { return m_deviceName; }
    
    // 设备计算函数（纯虚函数，子类必须实现）
    virtual void compute() = 0;
    
    // 设备初始化（虚函数，子类可选择重写）
    virtual bool initialize() { return true; }
    
    // 设备清理（虚函数，子类可选择重写）
    virtual void cleanup() {}

protected:
    // 参数设置和获取的便捷方法
    template<typename T>
    void setParameter(const std::string& paramName, const T& value);
    
    template<typename T>
    T getParameter(const std::string& paramName, const T& defaultValue = T{}) const;
    
    // 检查参数是否存在
    bool hasParameter(const std::string& paramName) const;
    
    // 删除参数
    void removeParameter(const std::string& paramName);
    
    // 获取所有参数
    std::unordered_map<std::string, std::string> getAllParameters() const;

private:
    std::string m_deviceName;
    ConfigManager& m_configManager;
};

// 模板函数实现
template<typename T>
void BaseDevice::setParameter(const std::string& paramName, const T& value) {
    m_configManager.setParameter(m_deviceName, paramName, value);
}

template<typename T>
T BaseDevice::getParameter(const std::string& paramName, const T& defaultValue) const {
    return m_configManager.getParameter(m_deviceName, paramName, defaultValue);
}
