cmake_minimum_required(VERSION 3.10)
project(DeviceConfigSystem)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -pedantic")
endif()

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 查找线程库
find_package(Threads REQUIRED)

# 添加头文件
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# 源文件列表
set(SOURCES
    main.cpp
    ConfigManager.cpp
    BaseDevice.cpp
    Motor.cpp
    Pump.cpp
    Inverter.cpp
    DeviceManager.cpp
)

# 头文件列表
set(HEADERS
    ConfigManager.h
    BaseDevice.h
    Motor.h
    Pump.h
    Inverter.h
    DeviceManager.h
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# 链接线程库
target_link_libraries(${PROJECT_NAME} Threads::Threads)

# 设置调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE DEBUG)
endif()

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

# 复制配置文件模板
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/device_config_template.txt
    ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/device_config_template.txt
    COPYONLY
)
