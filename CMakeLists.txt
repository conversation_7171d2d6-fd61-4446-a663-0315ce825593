cmake_minimum_required(VERSION 3.10)
project(ConfigManagerTest)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -pedantic")
endif()

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# 查找线程库
find_package(Threads REQUIRED)

# 添加头文件
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# 配置管理器核心文件
set(CONFIG_SOURCES
    ConfigManager.cpp
)

set(CONFIG_HEADERS
    ConfigManager.h
)

# 创建配置管理器测试程序
add_executable(ConfigTest config_test.cpp ${CONFIG_SOURCES} ${CONFIG_HEADERS})

# 链接线程库
target_link_libraries(ConfigTest Threads::Threads)

# 设置调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(ConfigTest PRIVATE DEBUG)
endif()

# 安装规则
install(TARGETS ConfigTest
    RUNTIME DESTINATION bin
)
