#include "ConfigManager.h"
#include <iostream>
#include <sstream>

ConfigManager &ConfigManager::getInstance()
{
    static ConfigManager instance;
    return instance;
}

ConfigManager::ConfigManager()
    : m_configFile("device_config.txt"), m_batchWriteInterval(100),
      m_writeThreadRunning(false), m_totalWrites(0), m_batchWrites(0),
      m_totalBatchSize(0), m_totalWriteTime(0.0)
{

    // 启动异步写入线程
    m_writeThreadRunning.store(true);
    m_writeThread = std::make_unique<std::thread>(&ConfigManager::writeThread, this);

    // 启动时加载配置文件
    loadFromFile();

    std::cout << "ConfigManager initialized with async write thread" << std::endl;
}

ConfigManager::~ConfigManager()
{
    // 停止写入线程
    m_writeThreadRunning.store(false);
    m_writeCondition.notify_all();

    if (m_writeThread && m_writeThread->joinable())
    {
        m_writeThread->join();
    }

    // 确保所有待写入的数据都已保存
    flushToDisk();

    std::cout << "ConfigManager destroyed" << std::endl;
}

std::string ConfigManager::makeKey(const std::string &deviceName, const std::string &paramName) const
{
    return deviceName + "::" + paramName;
}

bool ConfigManager::hasParameter(const std::string &deviceName, const std::string &paramName) const
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    std::string key = makeKey(deviceName, paramName);
    return m_config.find(key) != m_config.end();
}

void ConfigManager::removeParameter(const std::string &deviceName, const std::string &paramName)
{
    std::string key = makeKey(deviceName, paramName);

    // 从内存中删除
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        auto it = m_config.find(key);
        if (it != m_config.end())
        {
            m_config.erase(it);
        }
        else
        {
            return; // 参数不存在，无需删除
        }
    }

    // 异步写入删除操作（使用空值表示删除）
    {
        std::lock_guard<std::mutex> lock(m_writeMutex);
        m_writeQueue.push({key, ""}); // 空值表示删除
        m_writeCondition.notify_one();
    }
}

bool ConfigManager::loadFromFile(const std::string &filename)
{
    std::lock_guard<std::mutex> lock(m_configMutex);

    if (!filename.empty())
    {
        m_configFile = filename;
    }

    std::ifstream file(m_configFile);
    if (!file.is_open())
    {
        std::cout << "Warning: Could not open config file: " << m_configFile << std::endl;
        return false;
    }

    m_config.clear();
    std::string line;

    while (std::getline(file, line))
    {
        // 跳过空行和注释行
        if (line.empty() || line[0] == '#')
        {
            continue;
        }

        // 解析键值对 (格式: key=value)
        size_t pos = line.find('=');
        if (pos != std::string::npos)
        {
            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);

            // 去除前后空格
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);

            m_config[key] = value;
        }
    }

    file.close();
    std::cout << "Loaded " << m_config.size() << " parameters from " << m_configFile << std::endl;
    return true;
}

void ConfigManager::flushToDisk()
{
    // 强制立即处理所有待写入的参数
    {
        std::lock_guard<std::mutex> lock(m_writeMutex);
        m_writeCondition.notify_all();
    }

    // 等待写入队列清空
    while (true)
    {
        std::lock_guard<std::mutex> lock(m_writeMutex);
        if (m_writeQueue.empty())
        {
            break;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

void ConfigManager::setConfigFile(const std::string &filename)
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_configFile = filename;
}

std::unordered_map<std::string, std::string> ConfigManager::getDeviceParameters(const std::string &deviceName) const
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    std::unordered_map<std::string, std::string> deviceParams;

    std::string prefix = deviceName + "::";
    for (const auto &pair : m_config)
    {
        if (pair.first.substr(0, prefix.length()) == prefix)
        {
            std::string paramName = pair.first.substr(prefix.length());
            deviceParams[paramName] = pair.second;
        }
    }

    return deviceParams;
}

void ConfigManager::setBatchWriteInterval(int intervalMs)
{
    m_batchWriteInterval = intervalMs;
}

ConfigManager::PerformanceStats ConfigManager::getPerformanceStats() const
{
    std::lock_guard<std::mutex> lock(m_statsMutex);

    PerformanceStats stats;
    stats.totalWrites = m_totalWrites;
    stats.batchWrites = m_batchWrites;
    stats.averageBatchSize = (m_batchWrites > 0) ? (double)m_totalBatchSize / m_batchWrites : 0.0;
    stats.totalWriteTime = m_totalWriteTime;

    return stats;
}

void ConfigManager::writeThread()
{
    std::cout << "Async write thread started" << std::endl;

    while (m_writeThreadRunning.load())
    {
        std::unique_lock<std::mutex> lock(m_writeMutex);

        // 等待有数据需要写入或线程停止
        m_writeCondition.wait_for(lock, std::chrono::milliseconds(m_batchWriteInterval),
                                  [this]
                                  { return !m_writeQueue.empty() || !m_writeThreadRunning.load(); });

        if (!m_writeThreadRunning.load() && m_writeQueue.empty())
        {
            break;
        }

        // 执行批量写入
        if (!m_writeQueue.empty())
        {
            performBatchWrite();
        }
    }

    std::cout << "Async write thread ended" << std::endl;
}

void ConfigManager::performBatchWrite()
{
    auto startTime = std::chrono::high_resolution_clock::now();

    // 收集当前队列中的所有待写入项
    std::unordered_map<std::string, std::string> batchUpdates;
    size_t batchSize = 0;

    // 注意：调用此函数时已经持有m_writeMutex锁
    while (!m_writeQueue.empty())
    {
        auto item = m_writeQueue.front();
        m_writeQueue.pop();
        batchUpdates[item.first] = item.second;
        batchSize++;
    }

    if (batchUpdates.empty())
    {
        return;
    }

    // 读取当前配置文件内容
    std::unordered_map<std::string, std::string> fileConfig;
    std::ifstream inFile(m_configFile);
    if (inFile.is_open())
    {
        std::string line;
        while (std::getline(inFile, line))
        {
            if (line.empty() || line[0] == '#')
            {
                continue;
            }

            size_t pos = line.find('=');
            if (pos != std::string::npos)
            {
                std::string key = line.substr(0, pos);
                std::string value = line.substr(pos + 1);

                // 去除前后空格
                key.erase(0, key.find_first_not_of(" \t"));
                key.erase(key.find_last_not_of(" \t") + 1);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t") + 1);

                fileConfig[key] = value;
            }
        }
        inFile.close();
    }

    // 应用批量更新
    for (const auto &update : batchUpdates)
    {
        if (update.second.empty())
        {
            // 空值表示删除
            fileConfig.erase(update.first);
        }
        else
        {
            fileConfig[update.first] = update.second;
        }
    }

    // 写入更新后的配置文件
    std::ofstream outFile(m_configFile);
    if (outFile.is_open())
    {
        // 写入文件头注释
        outFile << "# Device Configuration File\n";
        outFile << "# Format: DeviceName::ParameterName=Value\n";
        outFile << "# Auto-generated, do not edit manually\n\n";

        // 写入所有配置项
        for (const auto &pair : fileConfig)
        {
            outFile << pair.first << "=" << pair.second << "\n";
        }

        outFile.close();
    }
    else
    {
        std::cerr << "Error: Could not open config file for batch writing: " << m_configFile << std::endl;
    }

    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double, std::milli>(endTime - startTime);

    // 更新统计信息
    {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_totalWrites += batchSize;
        m_batchWrites++;
        m_totalBatchSize += batchSize;
        m_totalWriteTime += duration.count();
    }

    std::cout << "Batch write completed: " << batchSize << " parameters in "
              << duration.count() << " ms" << std::endl;
}
