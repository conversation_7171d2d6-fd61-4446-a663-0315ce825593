#include "ConfigManager.h"
#include <iostream>
#include <sstream>

ConfigManager& ConfigManager::getInstance() {
    static ConfigManager instance;
    return instance;
}

ConfigManager::ConfigManager() 
    : m_configFile("device_config.txt"), m_needSave(false) {
    // 启动时加载配置文件
    loadFromFile();
}

ConfigManager::~ConfigManager() {
    // 析构时保存配置
    if (m_needSave.load()) {
        saveToFile();
    }
}

std::string ConfigManager::makeKey(const std::string& deviceName, const std::string& paramName) const {
    return deviceName + "::" + paramName;
}

std::pair<std::string, std::string> ConfigManager::parseKey(const std::string& key) const {
    size_t pos = key.find("::");
    if (pos != std::string::npos) {
        return {key.substr(0, pos), key.substr(pos + 2)};
    }
    return {"", ""};
}

bool ConfigManager::hasParameter(const std::string& deviceName, const std::string& paramName) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    std::string key = makeKey(deviceName, paramName);
    return m_config.find(key) != m_config.end();
}

void ConfigManager::removeParameter(const std::string& deviceName, const std::string& paramName) {
    std::lock_guard<std::mutex> lock(m_mutex);
    std::string key = makeKey(deviceName, paramName);
    auto it = m_config.find(key);
    if (it != m_config.end()) {
        m_config.erase(it);
        writeToFileImmediate();
    }
}

bool ConfigManager::loadFromFile(const std::string& filename) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!filename.empty()) {
        m_configFile = filename;
    }
    
    std::ifstream file(m_configFile);
    if (!file.is_open()) {
        std::cout << "Warning: Could not open config file: " << m_configFile << std::endl;
        return false;
    }
    
    m_config.clear();
    std::string line;
    
    while (std::getline(file, line)) {
        // 跳过空行和注释行
        if (line.empty() || line[0] == '#') {
            continue;
        }
        
        // 解析键值对 (格式: key=value)
        size_t pos = line.find('=');
        if (pos != std::string::npos) {
            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);
            
            // 去除前后空格
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);
            
            m_config[key] = value;
        }
    }
    
    file.close();
    std::cout << "Loaded " << m_config.size() << " parameters from " << m_configFile << std::endl;
    return true;
}

bool ConfigManager::saveToFile(const std::string& filename) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!filename.empty()) {
        m_configFile = filename;
    }
    
    std::ofstream file(m_configFile);
    if (!file.is_open()) {
        std::cerr << "Error: Could not open config file for writing: " << m_configFile << std::endl;
        return false;
    }
    
    // 写入文件头注释
    file << "# Device Configuration File\n";
    file << "# Format: DeviceName::ParameterName=Value\n";
    file << "# Auto-generated, do not edit manually\n\n";
    
    // 写入所有配置项
    for (const auto& pair : m_config) {
        file << pair.first << "=" << pair.second << "\n";
    }
    
    file.close();
    m_needSave.store(false);
    return true;
}

void ConfigManager::writeToFileImmediate() {
    // 注意：此函数假设调用者已经持有锁
    std::ofstream file(m_configFile);
    if (!file.is_open()) {
        std::cerr << "Error: Could not open config file for immediate writing: " << m_configFile << std::endl;
        return;
    }
    
    // 写入文件头注释
    file << "# Device Configuration File\n";
    file << "# Format: DeviceName::ParameterName=Value\n";
    file << "# Auto-generated, do not edit manually\n\n";
    
    // 写入所有配置项
    for (const auto& pair : m_config) {
        file << pair.first << "=" << pair.second << "\n";
    }
    
    file.close();
}

void ConfigManager::setConfigFile(const std::string& filename) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_configFile = filename;
}

std::unordered_map<std::string, std::string> ConfigManager::getDeviceParameters(const std::string& deviceName) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    std::unordered_map<std::string, std::string> deviceParams;
    
    std::string prefix = deviceName + "::";
    for (const auto& pair : m_config) {
        if (pair.first.substr(0, prefix.length()) == prefix) {
            std::string paramName = pair.first.substr(prefix.length());
            deviceParams[paramName] = pair.second;
        }
    }
    
    return deviceParams;
}
