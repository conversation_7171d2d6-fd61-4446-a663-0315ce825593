#pragma once

#include <string>
#include <unordered_map>
#include <mutex>
#include <fstream>
#include <memory>
#include <thread>
#include <atomic>
#include <chrono>

/**
 * 高效的实时配置文件管理器
 * 支持线程安全的参数读写和实时文件同步
 */
class ConfigManager {
public:
    static ConfigManager& getInstance();
    
    // 设置参数值（立即写入文件）
    template<typename T>
    void setParameter(const std::string& deviceName, const std::string& paramName, const T& value);
    
    // 获取参数值
    template<typename T>
    T getParameter(const std::string& deviceName, const std::string& paramName, const T& defaultValue = T{});
    
    // 检查参数是否存在
    bool hasParameter(const std::string& deviceName, const std::string& paramName) const;
    
    // 删除参数
    void removeParameter(const std::string& deviceName, const std::string& paramName);
    
    // 加载配置文件
    bool loadFromFile(const std::string& filename = "device_config.txt");
    
    // 保存到配置文件
    bool saveToFile(const std::string& filename = "device_config.txt");
    
    // 设置配置文件路径
    void setConfigFile(const std::string& filename);
    
    // 获取设备的所有参数
    std::unordered_map<std::string, std::string> getDeviceParameters(const std::string& deviceName) const;

private:
    ConfigManager();
    ~ConfigManager();
    
    // 禁用拷贝构造和赋值
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;
    
    // 生成键值
    std::string makeKey(const std::string& deviceName, const std::string& paramName) const;
    
    // 解析键值
    std::pair<std::string, std::string> parseKey(const std::string& key) const;
    
    // 立即写入文件
    void writeToFileImmediate();
    
    // 从字符串转换为指定类型
    template<typename T>
    T fromString(const std::string& str) const;
    
    // 从指定类型转换为字符串
    template<typename T>
    std::string toString(const T& value) const;

private:
    mutable std::mutex m_mutex;                           // 线程安全锁
    std::unordered_map<std::string, std::string> m_config; // 配置存储
    std::string m_configFile;                             // 配置文件路径
    std::atomic<bool> m_needSave;                         // 是否需要保存标志
};

// 模板函数实现
template<typename T>
void ConfigManager::setParameter(const std::string& deviceName, const std::string& paramName, const T& value) {
    std::lock_guard<std::mutex> lock(m_mutex);
    std::string key = makeKey(deviceName, paramName);
    m_config[key] = toString(value);
    
    // 立即写入文件
    writeToFileImmediate();
}

template<typename T>
T ConfigManager::getParameter(const std::string& deviceName, const std::string& paramName, const T& defaultValue) {
    std::lock_guard<std::mutex> lock(m_mutex);
    std::string key = makeKey(deviceName, paramName);
    
    auto it = m_config.find(key);
    if (it != m_config.end()) {
        return fromString<T>(it->second);
    }
    
    return defaultValue;
}

template<typename T>
T ConfigManager::fromString(const std::string& str) const {
    if constexpr (std::is_same_v<T, std::string>) {
        return str;
    } else if constexpr (std::is_same_v<T, int>) {
        return std::stoi(str);
    } else if constexpr (std::is_same_v<T, double>) {
        return std::stod(str);
    } else if constexpr (std::is_same_v<T, float>) {
        return std::stof(str);
    } else if constexpr (std::is_same_v<T, bool>) {
        return str == "true" || str == "1";
    } else if constexpr (std::is_same_v<T, long>) {
        return std::stol(str);
    } else if constexpr (std::is_same_v<T, long long>) {
        return std::stoll(str);
    } else {
        static_assert(sizeof(T) == 0, "Unsupported type for fromString");
    }
}

template<typename T>
std::string ConfigManager::toString(const T& value) const {
    if constexpr (std::is_same_v<T, std::string>) {
        return value;
    } else if constexpr (std::is_same_v<T, bool>) {
        return value ? "true" : "false";
    } else {
        return std::to_string(value);
    }
}
