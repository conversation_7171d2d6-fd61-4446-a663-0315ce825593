#pragma once

#include <string>
#include <unordered_map>
#include <mutex>
#include <fstream>
#include <memory>
#include <thread>
#include <atomic>
#include <chrono>
#include <queue>
#include <condition_variable>

/**
 * 高效的实时配置文件管理器
 * 优化策略：
 * 1. 增量写入：只写入修改的参数，而不是重写整个文件
 * 2. 批量写入：收集一段时间内的修改，批量写入减少IO
 * 3. 异步写入：使用独立线程处理文件写入，不阻塞主线程
 * 4. 写入缓冲：使用队列缓存待写入的参数
 */
class ConfigManager
{
public:
    static ConfigManager &getInstance();

    // 设置参数值（异步写入文件）
    template <typename T>
    void setParameter(const std::string &deviceName, const std::string &paramName, const T &value);

    // 获取参数值
    template <typename T>
    T getParameter(const std::string &deviceName, const std::string &paramName, const T &defaultValue = T{});

    // 检查参数是否存在
    bool hasParameter(const std::string &deviceName, const std::string &paramName) const;

    // 删除参数
    void removeParameter(const std::string &deviceName, const std::string &paramName);

    // 加载配置文件
    bool loadFromFile(const std::string &filename = "device_config.txt");

    // 强制立即保存所有待写入的参数
    void flushToDisk();

    // 设置配置文件路径
    void setConfigFile(const std::string &filename);

    // 获取设备的所有参数
    std::unordered_map<std::string, std::string> getDeviceParameters(const std::string &deviceName) const;

    // 设置批量写入间隔（毫秒）
    void setBatchWriteInterval(int intervalMs);

    // 获取性能统计
    struct PerformanceStats
    {
        uint64_t totalWrites;
        uint64_t batchWrites;
        double averageBatchSize;
        double totalWriteTime;
    };
    PerformanceStats getPerformanceStats() const;

private:
    ConfigManager();
    ~ConfigManager();

    // 禁用拷贝构造和赋值
    ConfigManager(const ConfigManager &) = delete;
    ConfigManager &operator=(const ConfigManager &) = delete;

    // 生成键值
    std::string makeKey(const std::string &deviceName, const std::string &paramName) const;

    // 异步写入线程函数
    void writeThread();

    // 执行批量写入
    void performBatchWrite();

    // 从字符串转换为指定类型
    template <typename T>
    T fromString(const std::string &str) const;

    // 从指定类型转换为字符串
    template <typename T>
    std::string toString(const T &value) const;

private:
    mutable std::mutex m_configMutex;                      // 配置数据保护锁
    std::unordered_map<std::string, std::string> m_config; // 配置存储

    // 异步写入相关
    std::mutex m_writeMutex;                                      // 写入队列保护锁
    std::condition_variable m_writeCondition;                     // 写入条件变量
    std::queue<std::pair<std::string, std::string>> m_writeQueue; // 待写入队列
    std::atomic<bool> m_writeThreadRunning;                       // 写入线程运行标志
    std::unique_ptr<std::thread> m_writeThread;                   // 写入线程

    std::string m_configFile; // 配置文件路径
    int m_batchWriteInterval; // 批量写入间隔（毫秒）

    // 性能统计
    mutable std::mutex m_statsMutex; // 统计数据保护锁
    uint64_t m_totalWrites;          // 总写入次数
    uint64_t m_batchWrites;          // 批量写入次数
    uint64_t m_totalBatchSize;       // 总批量大小
    double m_totalWriteTime;         // 总写入时间
};

// 模板函数实现
template <typename T>
void ConfigManager::setParameter(const std::string &deviceName, const std::string &paramName, const T &value)
{
    std::string key = makeKey(deviceName, paramName);
    std::string valueStr = toString(value);

    // 更新内存中的配置
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        m_config[key] = valueStr;
    }

    // 异步写入文件
    {
        std::lock_guard<std::mutex> lock(m_writeMutex);
        m_writeQueue.push({key, valueStr});
        m_writeCondition.notify_one();
    }
}

template <typename T>
T ConfigManager::getParameter(const std::string &deviceName, const std::string &paramName, const T &defaultValue)
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    std::string key = makeKey(deviceName, paramName);

    auto it = m_config.find(key);
    if (it != m_config.end())
    {
        return fromString<T>(it->second);
    }

    return defaultValue;
}

template <typename T>
T ConfigManager::fromString(const std::string &str) const
{
    if constexpr (std::is_same_v<T, std::string>)
    {
        return str;
    }
    else if constexpr (std::is_same_v<T, int>)
    {
        return std::stoi(str);
    }
    else if constexpr (std::is_same_v<T, double>)
    {
        return std::stod(str);
    }
    else if constexpr (std::is_same_v<T, float>)
    {
        return std::stof(str);
    }
    else if constexpr (std::is_same_v<T, bool>)
    {
        return str == "true" || str == "1";
    }
    else if constexpr (std::is_same_v<T, long>)
    {
        return std::stol(str);
    }
    else if constexpr (std::is_same_v<T, long long>)
    {
        return std::stoll(str);
    }
    else
    {
        static_assert(sizeof(T) == 0, "Unsupported type for fromString");
    }
}

template <typename T>
std::string ConfigManager::toString(const T &value) const
{
    if constexpr (std::is_same_v<T, std::string>)
    {
        return value;
    }
    else if constexpr (std::is_same_v<T, bool>)
    {
        return value ? "true" : "false";
    }
    else
    {
        return std::to_string(value);
    }
}
