#include "DeviceManager.h"
#include <iostream>
#include <algorithm>
#include <limits>

DeviceManager& DeviceManager::getInstance() {
    static DeviceManager instance;
    return instance;
}

DeviceManager::DeviceManager() 
    : m_computeLoopRunning(false), m_computeIntervalMs(10),
      m_totalCycles(0), m_totalComputeTime(0.0), 
      m_maxComputeTime(0.0), m_minComputeTime(std::numeric_limits<double>::max()) {
    std::cout << "DeviceManager initialized" << std::endl;
}

DeviceManager::~DeviceManager() {
    stopComputeLoop();
    cleanupAllDevices();
    std::cout << "DeviceManager destroyed" << std::endl;
}

void DeviceManager::addDevice(std::shared_ptr<BaseDevice> device) {
    if (!device) {
        std::cerr << "Error: Cannot add null device" << std::endl;
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    // 检查是否已存在同名设备
    auto it = std::find_if(m_devices.begin(), m_devices.end(),
        [&device](const std::shared_ptr<BaseDevice>& existingDevice) {
            return existingDevice->getName() == device->getName();
        });
    
    if (it != m_devices.end()) {
        std::cerr << "Warning: Device with name '" << device->getName() 
                  << "' already exists. Replacing..." << std::endl;
        *it = device;
    } else {
        m_devices.push_back(device);
        std::cout << "Device '" << device->getName() << "' added to manager" << std::endl;
    }
}

std::shared_ptr<BaseDevice> DeviceManager::findDevice(const std::string& deviceName) {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    auto it = std::find_if(m_devices.begin(), m_devices.end(),
        [&deviceName](const std::shared_ptr<BaseDevice>& device) {
            return device->getName() == deviceName;
        });
    
    return (it != m_devices.end()) ? *it : nullptr;
}

bool DeviceManager::removeDevice(const std::string& deviceName) {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    auto it = std::find_if(m_devices.begin(), m_devices.end(),
        [&deviceName](const std::shared_ptr<BaseDevice>& device) {
            return device->getName() == deviceName;
        });
    
    if (it != m_devices.end()) {
        (*it)->cleanup();
        m_devices.erase(it);
        std::cout << "Device '" << deviceName << "' removed from manager" << std::endl;
        return true;
    }
    
    std::cerr << "Warning: Device '" << deviceName << "' not found" << std::endl;
    return false;
}

std::vector<std::shared_ptr<BaseDevice>> DeviceManager::getAllDevices() const {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    return m_devices;
}

size_t DeviceManager::getDeviceCount() const {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    return m_devices.size();
}

void DeviceManager::startComputeLoop(int intervalMs) {
    if (m_computeLoopRunning.load()) {
        std::cout << "Compute loop is already running" << std::endl;
        return;
    }
    
    m_computeIntervalMs = intervalMs;
    m_computeLoopRunning.store(true);
    
    m_computeThread = std::make_unique<std::thread>(&DeviceManager::computeLoop, this);
    
    std::cout << "Compute loop started with interval: " << intervalMs << "ms" << std::endl;
}

void DeviceManager::stopComputeLoop() {
    if (!m_computeLoopRunning.load()) {
        return;
    }
    
    m_computeLoopRunning.store(false);
    
    if (m_computeThread && m_computeThread->joinable()) {
        m_computeThread->join();
    }
    
    m_computeThread.reset();
    std::cout << "Compute loop stopped" << std::endl;
}

bool DeviceManager::isComputeLoopRunning() const {
    return m_computeLoopRunning.load();
}

void DeviceManager::computeOnce() {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    std::vector<std::shared_ptr<BaseDevice>> devices;
    {
        std::lock_guard<std::mutex> lock(m_devicesMutex);
        devices = m_devices;  // 复制一份，避免长时间持锁
    }
    
    // 执行所有设备的计算
    for (auto& device : devices) {
        if (device) {
            try {
                device->compute();
            } catch (const std::exception& e) {
                std::cerr << "Error computing device '" << device->getName() 
                          << "': " << e.what() << std::endl;
            }
        }
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double, std::milli>(endTime - startTime);
    
    updateStats(duration.count());
}

bool DeviceManager::initializeAllDevices() {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    bool allSuccess = true;
    for (auto& device : m_devices) {
        if (device) {
            try {
                if (!device->initialize()) {
                    std::cerr << "Failed to initialize device: " << device->getName() << std::endl;
                    allSuccess = false;
                }
            } catch (const std::exception& e) {
                std::cerr << "Exception initializing device '" << device->getName() 
                          << "': " << e.what() << std::endl;
                allSuccess = false;
            }
        }
    }
    
    std::cout << "Device initialization " << (allSuccess ? "completed successfully" : "completed with errors") << std::endl;
    return allSuccess;
}

void DeviceManager::cleanupAllDevices() {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    for (auto& device : m_devices) {
        if (device) {
            try {
                device->cleanup();
            } catch (const std::exception& e) {
                std::cerr << "Exception cleaning up device '" << device->getName() 
                          << "': " << e.what() << std::endl;
            }
        }
    }
    
    std::cout << "All devices cleaned up" << std::endl;
}

void DeviceManager::printAllDevicesStatus() const {
    std::vector<std::shared_ptr<BaseDevice>> devices;
    {
        std::lock_guard<std::mutex> lock(m_devicesMutex);
        devices = m_devices;
    }
    
    std::cout << "\n========== All Devices Status ==========" << std::endl;
    std::cout << "Total devices: " << devices.size() << std::endl;
    
    for (const auto& device : devices) {
        if (device) {
            std::cout << "Device: " << device->getName() << std::endl;
            auto params = device->getAllParameters();
            for (const auto& param : params) {
                std::cout << "  " << param.first << " = " << param.second << std::endl;
            }
        }
    }
    std::cout << "=======================================" << std::endl;
}

DeviceManager::ComputeStats DeviceManager::getComputeStats() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    ComputeStats stats;
    stats.totalCycles = m_totalCycles;
    stats.averageComputeTime = (m_totalCycles > 0) ? (m_totalComputeTime / m_totalCycles) : 0.0;
    stats.maxComputeTime = m_maxComputeTime;
    stats.minComputeTime = (m_minComputeTime == std::numeric_limits<double>::max()) ? 0.0 : m_minComputeTime;
    
    return stats;
}

void DeviceManager::resetComputeStats() {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    m_totalCycles = 0;
    m_totalComputeTime = 0.0;
    m_maxComputeTime = 0.0;
    m_minComputeTime = std::numeric_limits<double>::max();
    
    std::cout << "Compute statistics reset" << std::endl;
}

void DeviceManager::computeLoop() {
    std::cout << "Compute loop thread started" << std::endl;
    
    while (m_computeLoopRunning.load()) {
        computeOnce();
        
        // 休眠指定时间
        std::this_thread::sleep_for(std::chrono::milliseconds(m_computeIntervalMs));
    }
    
    std::cout << "Compute loop thread ended" << std::endl;
}

void DeviceManager::updateStats(double computeTime) {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    m_totalCycles++;
    m_totalComputeTime += computeTime;
    m_maxComputeTime = std::max(m_maxComputeTime, computeTime);
    m_minComputeTime = std::min(m_minComputeTime, computeTime);
}
