#pragma once

#include "BaseDevice.h"
#include <vector>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <chrono>

/**
 * 设备管理器类
 * 负责管理所有设备实例，提供设备的添加、删除、查找功能
 * 并提供循环计算线程
 */
class DeviceManager {
public:
    static DeviceManager& getInstance();
    
    // 添加设备
    void addDevice(std::shared_ptr<BaseDevice> device);
    
    // 根据名称查找设备
    std::shared_ptr<BaseDevice> findDevice(const std::string& deviceName);
    
    // 删除设备
    bool removeDevice(const std::string& deviceName);
    
    // 获取所有设备
    std::vector<std::shared_ptr<BaseDevice>> getAllDevices() const;
    
    // 获取设备数量
    size_t getDeviceCount() const;
    
    // 启动计算循环线程
    void startComputeLoop(int intervalMs = 10);
    
    // 停止计算循环线程
    void stopComputeLoop();
    
    // 检查计算循环是否在运行
    bool isComputeLoopRunning() const;
    
    // 手动执行一次所有设备的计算
    void computeOnce();
    
    // 初始化所有设备
    bool initializeAllDevices();
    
    // 清理所有设备
    void cleanupAllDevices();
    
    // 打印所有设备状态
    void printAllDevicesStatus() const;
    
    // 获取计算循环统计信息
    struct ComputeStats {
        uint64_t totalCycles;
        double averageComputeTime;
        double maxComputeTime;
        double minComputeTime;
    };
    
    ComputeStats getComputeStats() const;
    
    // 重置统计信息
    void resetComputeStats();

private:
    DeviceManager();
    ~DeviceManager();
    
    // 禁用拷贝构造和赋值
    DeviceManager(const DeviceManager&) = delete;
    DeviceManager& operator=(const DeviceManager&) = delete;
    
    // 计算循环函数
    void computeLoop();
    
    // 更新统计信息
    void updateStats(double computeTime);

private:
    mutable std::mutex m_devicesMutex;                    // 设备列表保护锁
    std::vector<std::shared_ptr<BaseDevice>> m_devices;   // 设备列表
    
    std::atomic<bool> m_computeLoopRunning;               // 计算循环运行标志
    std::unique_ptr<std::thread> m_computeThread;         // 计算线程
    int m_computeIntervalMs;                              // 计算间隔（毫秒）
    
    // 统计信息
    mutable std::mutex m_statsMutex;                      // 统计信息保护锁
    uint64_t m_totalCycles;                               // 总循环次数
    double m_totalComputeTime;                            // 总计算时间
    double m_maxComputeTime;                              // 最大计算时间
    double m_minComputeTime;                              // 最小计算时间
};
