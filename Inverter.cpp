#include "Inverter.h"
#include <algorithm>
#include <cmath>

Inverter::Inverter(const std::string& deviceName) 
    : BaseDevice(deviceName), m_totalEnergy(0.0), m_isRunning(false) {
    // 初始化默认参数（如果不存在的话）
    if (!hasParameter("frequency")) {
        setParameter("frequency", DEFAULT_FREQUENCY);
    }
    if (!hasParameter("voltage")) {
        setParameter("voltage", DEFAULT_VOLTAGE);
    }
    if (!hasParameter("current")) {
        setParameter("current", DEFAULT_CURRENT);
    }
    if (!hasParameter("maxFrequency")) {
        setParameter("maxFrequency", DEFAULT_MAX_FREQUENCY);
    }
    if (!hasParameter("maxVoltage")) {
        setParameter("maxVoltage", DEFAULT_MAX_VOLTAGE);
    }
    if (!hasParameter("maxCurrent")) {
        setParameter("maxCurrent", DEFAULT_MAX_CURRENT);
    }
    if (!hasParameter("enabled")) {
        setParameter("enabled", DEFAULT_ENABLED);
    }
}

bool Inverter::initialize() {
    std::cout << "Initializing inverter: " << getName() << std::endl;
    m_totalEnergy = 0.0;
    m_isRunning = false;
    return true;
}

void Inverter::compute() {
    if (!isEnabled()) {
        m_isRunning = false;
        return;
    }
    
    double frequency = getFrequency();
    double voltage = getVoltage();
    double current = getCurrent();
    
    if (frequency > 0.01 && voltage > 0.01) {  // 如果频率和电压大于阈值
        m_isRunning = true;
        
        // 计算功率并累积能量
        double power = getPower();
        m_totalEnergy += power * 0.01 / 3600.0;  // 假设计算周期为10ms，转换为kWh
        
        // 限制最大值
        double maxFrequency = getMaxFrequency();
        if (frequency > maxFrequency) {
            setFrequency(maxFrequency);
        }
        
        double maxVoltage = getMaxVoltage();
        if (voltage > maxVoltage) {
            setVoltage(maxVoltage);
        }
        
        double maxCurrent = getMaxCurrent();
        if (current > maxCurrent) {
            setCurrent(maxCurrent);
        }
    } else {
        m_isRunning = false;
    }
}

void Inverter::setFrequency(double frequency) {
    setParameter("frequency", frequency);
    std::cout << "Inverter " << getName() << " frequency set to: " << frequency << " Hz" << std::endl;
}

double Inverter::getFrequency() const {
    return getParameter("frequency", DEFAULT_FREQUENCY);
}

void Inverter::setVoltage(double voltage) {
    setParameter("voltage", voltage);
    std::cout << "Inverter " << getName() << " voltage set to: " << voltage << " V" << std::endl;
}

double Inverter::getVoltage() const {
    return getParameter("voltage", DEFAULT_VOLTAGE);
}

void Inverter::setCurrent(double current) {
    setParameter("current", current);
    std::cout << "Inverter " << getName() << " current set to: " << current << " A" << std::endl;
}

double Inverter::getCurrent() const {
    return getParameter("current", DEFAULT_CURRENT);
}

void Inverter::setEnabled(bool enabled) {
    setParameter("enabled", enabled);
    std::cout << "Inverter " << getName() << " " 
              << (enabled ? "enabled" : "disabled") << std::endl;
}

bool Inverter::isEnabled() const {
    return getParameter("enabled", DEFAULT_ENABLED);
}

void Inverter::setMaxFrequency(double maxFrequency) {
    setParameter("maxFrequency", maxFrequency);
    std::cout << "Inverter " << getName() << " max frequency set to: " << maxFrequency << " Hz" << std::endl;
}

double Inverter::getMaxFrequency() const {
    return getParameter("maxFrequency", DEFAULT_MAX_FREQUENCY);
}

void Inverter::setMaxVoltage(double maxVoltage) {
    setParameter("maxVoltage", maxVoltage);
    std::cout << "Inverter " << getName() << " max voltage set to: " << maxVoltage << " V" << std::endl;
}

double Inverter::getMaxVoltage() const {
    return getParameter("maxVoltage", DEFAULT_MAX_VOLTAGE);
}

void Inverter::setMaxCurrent(double maxCurrent) {
    setParameter("maxCurrent", maxCurrent);
    std::cout << "Inverter " << getName() << " max current set to: " << maxCurrent << " A" << std::endl;
}

double Inverter::getMaxCurrent() const {
    return getParameter("maxCurrent", DEFAULT_MAX_CURRENT);
}

double Inverter::getPower() const {
    // 简化的功率计算：P = √3 * V * I * cos(φ)，这里假设cos(φ) = 0.8
    return std::sqrt(3.0) * getVoltage() * getCurrent() * 0.8 / 1000.0;  // 转换为kW
}

void Inverter::printStatus() const {
    std::cout << "\n=== Inverter Status: " << getName() << " ===" << std::endl;
    std::cout << "Enabled: " << (isEnabled() ? "Yes" : "No") << std::endl;
    std::cout << "Frequency: " << getFrequency() << " Hz" << std::endl;
    std::cout << "Max Frequency: " << getMaxFrequency() << " Hz" << std::endl;
    std::cout << "Voltage: " << getVoltage() << " V" << std::endl;
    std::cout << "Max Voltage: " << getMaxVoltage() << " V" << std::endl;
    std::cout << "Current: " << getCurrent() << " A" << std::endl;
    std::cout << "Max Current: " << getMaxCurrent() << " A" << std::endl;
    std::cout << "Power: " << getPower() << " kW" << std::endl;
    std::cout << "Total Energy: " << m_totalEnergy << " kWh" << std::endl;
    std::cout << "Running: " << (m_isRunning ? "Yes" : "No") << std::endl;
    std::cout << "=========================" << std::endl;
}
