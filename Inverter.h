#pragma once

#include "BaseDevice.h"
#include <iostream>

/**
 * 变频器设备类
 * 继承自BaseDevice，实现变频器特有的功能
 */
class Inverter : public BaseDevice {
public:
    explicit Inverter(const std::string& deviceName);
    
    // 实现基类的纯虚函数
    void compute() override;
    
    // 初始化变频器
    bool initialize() override;
    
    // 变频器特有的方法
    void setFrequency(double frequency);
    double getFrequency() const;
    
    void setVoltage(double voltage);
    double getVoltage() const;
    
    void setCurrent(double current);
    double getCurrent() const;
    
    void setEnabled(bool enabled);
    bool isEnabled() const;
    
    void setMaxFrequency(double maxFrequency);
    double getMaxFrequency() const;
    
    void setMaxVoltage(double maxVoltage);
    double getMaxVoltage() const;
    
    void setMaxCurrent(double maxCurrent);
    double getMaxCurrent() const;
    
    // 获取变频器状态信息
    void printStatus() const;
    
    // 计算功率
    double getPower() const;

private:
    // 内部状态变量（运行时状态，不需要持久化）
    double m_totalEnergy;
    bool m_isRunning;
    
    // 默认参数值
    static constexpr double DEFAULT_FREQUENCY = 0.0;
    static constexpr double DEFAULT_VOLTAGE = 0.0;
    static constexpr double DEFAULT_CURRENT = 0.0;
    static constexpr double DEFAULT_MAX_FREQUENCY = 50.0;
    static constexpr double DEFAULT_MAX_VOLTAGE = 380.0;
    static constexpr double DEFAULT_MAX_CURRENT = 10.0;
    static constexpr bool DEFAULT_ENABLED = false;
};
