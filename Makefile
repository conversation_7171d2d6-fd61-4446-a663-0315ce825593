# Makefile for Device Configuration System
# Alternative to CMake for simple compilation

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -pedantic -O2
LDFLAGS = -pthread

# Debug flags
DEBUG_FLAGS = -g -DDEBUG -O0

# Directories
SRC_DIR = .
BUILD_DIR = build
BIN_DIR = bin

# Source files
SOURCES = ConfigManager.cpp BaseDevice.cpp Motor.cpp Pump.cpp Inverter.cpp DeviceManager.cpp
HEADERS = ConfigManager.h BaseDevice.h Motor.h Pump.h Inverter.h DeviceManager.h

# Object files
OBJECTS = $(SOURCES:%.cpp=$(BUILD_DIR)/%.o)

# Executables
MAIN_TARGET = $(BIN_DIR)/DeviceConfigSystem
TEST_TARGET = $(BIN_DIR)/TestExample

# Default target
all: directories $(MAIN_TARGET) $(TEST_TARGET)

# Create directories
directories:
	@mkdir -p $(BUILD_DIR)
	@mkdir -p $(BIN_DIR)

# Main executable
$(MAIN_TARGET): $(OBJECTS) $(BUILD_DIR)/main.o
	$(CXX) $(OBJECTS) $(BUILD_DIR)/main.o -o $@ $(LDFLAGS)
	@echo "Built main executable: $@"

# Test executable
$(TEST_TARGET): $(OBJECTS) $(BUILD_DIR)/test_example.o
	$(CXX) $(OBJECTS) $(BUILD_DIR)/test_example.o -o $@ $(LDFLAGS)
	@echo "Built test executable: $@"

# Object files compilation
$(BUILD_DIR)/%.o: $(SRC_DIR)/%.cpp
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Debug build
debug: CXXFLAGS += $(DEBUG_FLAGS)
debug: clean all

# Clean build files
clean:
	rm -rf $(BUILD_DIR)
	rm -rf $(BIN_DIR)
	@echo "Cleaned build files"

# Install (copy to system directory)
install: all
	@echo "Installing to /usr/local/bin (requires sudo)"
	sudo cp $(MAIN_TARGET) /usr/local/bin/
	sudo cp $(TEST_TARGET) /usr/local/bin/

# Run main program
run: $(MAIN_TARGET)
	./$(MAIN_TARGET)

# Run test program
test: $(TEST_TARGET)
	./$(TEST_TARGET)

# Check dependencies
deps:
	@echo "Checking dependencies..."
	@$(CXX) --version
	@echo "C++17 support: OK"

# Help
help:
	@echo "Available targets:"
	@echo "  all      - Build both main and test executables (default)"
	@echo "  debug    - Build with debug flags"
	@echo "  clean    - Remove build files"
	@echo "  install  - Install executables to system"
	@echo "  run      - Build and run main program"
	@echo "  test     - Build and run test program"
	@echo "  deps     - Check compiler dependencies"
	@echo "  help     - Show this help message"

# Phony targets
.PHONY: all directories debug clean install run test deps help

# Dependencies (header files)
$(BUILD_DIR)/ConfigManager.o: ConfigManager.h
$(BUILD_DIR)/BaseDevice.o: BaseDevice.h ConfigManager.h
$(BUILD_DIR)/Motor.o: Motor.h BaseDevice.h
$(BUILD_DIR)/Pump.o: Pump.h BaseDevice.h
$(BUILD_DIR)/Inverter.o: Inverter.h BaseDevice.h
$(BUILD_DIR)/DeviceManager.o: DeviceManager.h BaseDevice.h
$(BUILD_DIR)/main.o: DeviceManager.h Motor.h Pump.h Inverter.h
$(BUILD_DIR)/test_example.o: DeviceManager.h Motor.h Pump.h Inverter.h
