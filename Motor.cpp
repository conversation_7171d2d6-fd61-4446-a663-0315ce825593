#include "Motor.h"
#include <algorithm>
#include <cmath>

Motor::Motor(const std::string& deviceName) 
    : BaseDevice(deviceName), m_currentPosition(0.0), m_isRunning(false) {
    // 初始化默认参数（如果不存在的话）
    if (!hasParameter("speed")) {
        setParameter("speed", DEFAULT_SPEED);
    }
    if (!hasParameter("maxSpeed")) {
        setParameter("maxSpeed", DEFAULT_MAX_SPEED);
    }
    if (!hasParameter("acceleration")) {
        setParameter("acceleration", DEFAULT_ACCELERATION);
    }
    if (!hasParameter("direction")) {
        setParameter("direction", DEFAULT_DIRECTION);
    }
    if (!hasParameter("enabled")) {
        setParameter("enabled", DEFAULT_ENABLED);
    }
}

bool Motor::initialize() {
    std::cout << "Initializing motor: " << getName() << std::endl;
    m_currentPosition = 0.0;
    m_isRunning = false;
    return true;
}

void Motor::compute() {
    if (!isEnabled()) {
        m_isRunning = false;
        return;
    }
    
    double speed = getSpeed();
    if (std::abs(speed) > 0.01) {  // 如果速度大于阈值
        m_isRunning = true;
        
        // 模拟位置更新（简单的积分）
        double direction = getDirection() ? 1.0 : -1.0;
        m_currentPosition += speed * direction * 0.01;  // 假设计算周期为10ms
        
        // 限制最大速度
        double maxSpeed = getMaxSpeed();
        if (std::abs(speed) > maxSpeed) {
            speed = std::copysign(maxSpeed, speed);
            setSpeed(speed);
        }
    } else {
        m_isRunning = false;
    }
}

void Motor::setSpeed(double speed) {
    setParameter("speed", speed);
    std::cout << "Motor " << getName() << " speed set to: " << speed << std::endl;
}

double Motor::getSpeed() const {
    return getParameter("speed", DEFAULT_SPEED);
}

void Motor::setDirection(bool clockwise) {
    setParameter("direction", clockwise);
    std::cout << "Motor " << getName() << " direction set to: " 
              << (clockwise ? "clockwise" : "counter-clockwise") << std::endl;
}

bool Motor::getDirection() const {
    return getParameter("direction", DEFAULT_DIRECTION);
}

void Motor::setEnabled(bool enabled) {
    setParameter("enabled", enabled);
    std::cout << "Motor " << getName() << " " 
              << (enabled ? "enabled" : "disabled") << std::endl;
}

bool Motor::isEnabled() const {
    return getParameter("enabled", DEFAULT_ENABLED);
}

void Motor::setMaxSpeed(double maxSpeed) {
    setParameter("maxSpeed", maxSpeed);
    std::cout << "Motor " << getName() << " max speed set to: " << maxSpeed << std::endl;
}

double Motor::getMaxSpeed() const {
    return getParameter("maxSpeed", DEFAULT_MAX_SPEED);
}

void Motor::setAcceleration(double acceleration) {
    setParameter("acceleration", acceleration);
    std::cout << "Motor " << getName() << " acceleration set to: " << acceleration << std::endl;
}

double Motor::getAcceleration() const {
    return getParameter("acceleration", DEFAULT_ACCELERATION);
}

void Motor::printStatus() const {
    std::cout << "\n=== Motor Status: " << getName() << " ===" << std::endl;
    std::cout << "Enabled: " << (isEnabled() ? "Yes" : "No") << std::endl;
    std::cout << "Speed: " << getSpeed() << std::endl;
    std::cout << "Max Speed: " << getMaxSpeed() << std::endl;
    std::cout << "Direction: " << (getDirection() ? "Clockwise" : "Counter-clockwise") << std::endl;
    std::cout << "Acceleration: " << getAcceleration() << std::endl;
    std::cout << "Current Position: " << m_currentPosition << std::endl;
    std::cout << "Running: " << (m_isRunning ? "Yes" : "No") << std::endl;
    std::cout << "=========================" << std::endl;
}
