#pragma once

#include "BaseDevice.h"
#include <iostream>

/**
 * 电机设备类
 * 继承自BaseDevice，实现电机特有的功能
 */
class Motor : public BaseDevice {
public:
    explicit Motor(const std::string& deviceName);
    
    // 实现基类的纯虚函数
    void compute() override;
    
    // 初始化电机
    bool initialize() override;
    
    // 电机特有的方法
    void setSpeed(double speed);
    double getSpeed() const;
    
    void setDirection(bool clockwise);
    bool getDirection() const;
    
    void setEnabled(bool enabled);
    bool isEnabled() const;
    
    void setMaxSpeed(double maxSpeed);
    double getMaxSpeed() const;
    
    void setAcceleration(double acceleration);
    double getAcceleration() const;
    
    // 获取电机状态信息
    void printStatus() const;

private:
    // 内部状态变量（运行时状态，不需要持久化）
    double m_currentPosition;
    bool m_isRunning;
    
    // 默认参数值
    static constexpr double DEFAULT_SPEED = 0.0;
    static constexpr double DEFAULT_MAX_SPEED = 1000.0;
    static constexpr double DEFAULT_ACCELERATION = 100.0;
    static constexpr bool DEFAULT_DIRECTION = true;  // true = clockwise
    static constexpr bool DEFAULT_ENABLED = false;
};
