#include "Pump.h"
#include <algorithm>
#include <cmath>

Pump::Pump(const std::string& deviceName) 
    : BaseDevice(deviceName), m_totalVolume(0.0), m_isRunning(false) {
    // 初始化默认参数（如果不存在的话）
    if (!hasParameter("flowRate")) {
        setParameter("flowRate", DEFAULT_FLOW_RATE);
    }
    if (!hasParameter("pressure")) {
        setParameter("pressure", DEFAULT_PRESSURE);
    }
    if (!hasParameter("maxFlowRate")) {
        setParameter("maxFlowRate", DEFAULT_MAX_FLOW_RATE);
    }
    if (!hasParameter("maxPressure")) {
        setParameter("maxPressure", DEFAULT_MAX_PRESSURE);
    }
    if (!hasParameter("enabled")) {
        setParameter("enabled", DEFAULT_ENABLED);
    }
}

bool Pump::initialize() {
    std::cout << "Initializing pump: " << getName() << std::endl;
    m_totalVolume = 0.0;
    m_isRunning = false;
    return true;
}

void Pump::compute() {
    if (!isEnabled()) {
        m_isRunning = false;
        return;
    }
    
    double flowRate = getFlowRate();
    if (std::abs(flowRate) > 0.01) {  // 如果流量大于阈值
        m_isRunning = true;
        
        // 模拟体积累积
        m_totalVolume += flowRate * 0.01;  // 假设计算周期为10ms
        
        // 限制最大流量
        double maxFlowRate = getMaxFlowRate();
        if (std::abs(flowRate) > maxFlowRate) {
            flowRate = std::copysign(maxFlowRate, flowRate);
            setFlowRate(flowRate);
        }
        
        // 限制最大压力
        double pressure = getPressure();
        double maxPressure = getMaxPressure();
        if (pressure > maxPressure) {
            setPressure(maxPressure);
        }
    } else {
        m_isRunning = false;
    }
}

void Pump::setFlowRate(double flowRate) {
    setParameter("flowRate", flowRate);
    std::cout << "Pump " << getName() << " flow rate set to: " << flowRate << " L/min" << std::endl;
}

double Pump::getFlowRate() const {
    return getParameter("flowRate", DEFAULT_FLOW_RATE);
}

void Pump::setPressure(double pressure) {
    setParameter("pressure", pressure);
    std::cout << "Pump " << getName() << " pressure set to: " << pressure << " bar" << std::endl;
}

double Pump::getPressure() const {
    return getParameter("pressure", DEFAULT_PRESSURE);
}

void Pump::setEnabled(bool enabled) {
    setParameter("enabled", enabled);
    std::cout << "Pump " << getName() << " " 
              << (enabled ? "enabled" : "disabled") << std::endl;
}

bool Pump::isEnabled() const {
    return getParameter("enabled", DEFAULT_ENABLED);
}

void Pump::setMaxFlowRate(double maxFlowRate) {
    setParameter("maxFlowRate", maxFlowRate);
    std::cout << "Pump " << getName() << " max flow rate set to: " << maxFlowRate << " L/min" << std::endl;
}

double Pump::getMaxFlowRate() const {
    return getParameter("maxFlowRate", DEFAULT_MAX_FLOW_RATE);
}

void Pump::setMaxPressure(double maxPressure) {
    setParameter("maxPressure", maxPressure);
    std::cout << "Pump " << getName() << " max pressure set to: " << maxPressure << " bar" << std::endl;
}

double Pump::getMaxPressure() const {
    return getParameter("maxPressure", DEFAULT_MAX_PRESSURE);
}

void Pump::printStatus() const {
    std::cout << "\n=== Pump Status: " << getName() << " ===" << std::endl;
    std::cout << "Enabled: " << (isEnabled() ? "Yes" : "No") << std::endl;
    std::cout << "Flow Rate: " << getFlowRate() << " L/min" << std::endl;
    std::cout << "Max Flow Rate: " << getMaxFlowRate() << " L/min" << std::endl;
    std::cout << "Pressure: " << getPressure() << " bar" << std::endl;
    std::cout << "Max Pressure: " << getMaxPressure() << " bar" << std::endl;
    std::cout << "Total Volume: " << m_totalVolume << " L" << std::endl;
    std::cout << "Running: " << (m_isRunning ? "Yes" : "No") << std::endl;
    std::cout << "=========================" << std::endl;
}
