#pragma once

#include "BaseDevice.h"
#include <iostream>

/**
 * 泵设备类
 * 继承自BaseDevice，实现泵特有的功能
 */
class Pump : public BaseDevice {
public:
    explicit Pump(const std::string& deviceName);
    
    // 实现基类的纯虚函数
    void compute() override;
    
    // 初始化泵
    bool initialize() override;
    
    // 泵特有的方法
    void setFlowRate(double flowRate);
    double getFlowRate() const;
    
    void setPressure(double pressure);
    double getPressure() const;
    
    void setEnabled(bool enabled);
    bool isEnabled() const;
    
    void setMaxFlowRate(double maxFlowRate);
    double getMaxFlowRate() const;
    
    void setMaxPressure(double maxPressure);
    double getMaxPressure() const;
    
    // 获取泵状态信息
    void printStatus() const;

private:
    // 内部状态变量（运行时状态，不需要持久化）
    double m_totalVolume;
    bool m_isRunning;
    
    // 默认参数值
    static constexpr double DEFAULT_FLOW_RATE = 0.0;
    static constexpr double DEFAULT_PRESSURE = 0.0;
    static constexpr double DEFAULT_MAX_FLOW_RATE = 100.0;
    static constexpr double DEFAULT_MAX_PRESSURE = 10.0;
    static constexpr bool DEFAULT_ENABLED = false;
};
