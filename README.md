# 设备配置管理系统 (Device Configuration Management System)

一个基于C++17的高效实时设备参数配置管理系统，支持多种设备类型的参数实时读写和持久化存储。

## 功能特性

### 核心功能
- **实时参数管理**: 支持设备参数的实时读写，修改后立即保存到配置文件
- **线程安全**: 全面的线程安全设计，支持多线程环境下的并发操作
- **高效存储**: 基于哈希表的键值对存储，以"设备名::参数名"为键
- **自动持久化**: 参数修改后自动写入配置文件，无需手动保存
- **设备管理**: 统一的设备管理器，支持设备的添加、删除、查找
- **循环计算**: 独立线程进行设备状态循环计算，模拟真实设备运行

### 支持的设备类型
- **Motor (电机)**: 速度、方向、加速度等参数控制
- **Pump (泵)**: 流量、压力等参数控制  
- **Inverter (变频器)**: 频率、电压、电流等参数控制
- **可扩展**: 基于BaseDevice基类，易于添加新的设备类型

## 系统架构

### 类层次结构
```
BaseDevice (抽象基类)
├── Motor (电机设备)
├── Pump (泵设备)
└── Inverter (变频器设备)

ConfigManager (单例配置管理器)
DeviceManager (单例设备管理器)
```

### 核心组件

#### 1. ConfigManager (配置管理器)
- 单例模式，全局唯一的配置管理实例
- 线程安全的参数读写操作
- 实时文件同步，参数修改立即写入文件
- 支持多种数据类型：int, double, float, bool, string等

#### 2. BaseDevice (设备基类)
- 提供统一的设备接口
- 封装参数操作的便捷方法
- 纯虚函数compute()，子类必须实现具体的计算逻辑

#### 3. DeviceManager (设备管理器)
- 单例模式，管理所有设备实例
- 独立线程进行循环计算
- 设备的添加、删除、查找功能
- 计算性能统计

## 编译和运行

### 系统要求
- C++17 兼容的编译器 (GCC 7+, Clang 5+, MSVC 2017+)
- CMake 3.10+
- 支持std::thread的系统

### 编译步骤
```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake ..

# 编译
cmake --build .

# 运行
./bin/DeviceConfigSystem
```

### Windows (Visual Studio)
```cmd
mkdir build
cd build
cmake .. -G "Visual Studio 16 2019"
cmake --build . --config Release
.\bin\Release\DeviceConfigSystem.exe
```

## 使用示例

### 基本使用流程

1. **创建设备**
```cpp
auto motor = std::make_shared<Motor>("Motor1");
auto pump = std::make_shared<Pump>("Pump1");
auto inverter = std::make_shared<Inverter>("Inverter1");
```

2. **添加到设备管理器**
```cpp
auto& manager = DeviceManager::getInstance();
manager.addDevice(motor);
manager.addDevice(pump);
manager.addDevice(inverter);
```

3. **设置参数**
```cpp
motor->setSpeed(100.0);
motor->setEnabled(true);
pump->setFlowRate(25.5);
inverter->setFrequency(50.0);
```

4. **启动循环计算**
```cpp
manager.startComputeLoop(10); // 10ms间隔
```

### 配置文件格式

配置文件采用简单的键值对格式：
```
# 设备配置文件
# 格式: 设备名::参数名=值

Motor1::speed=100.0
Motor1::enabled=true
Motor1::maxSpeed=1000.0
Pump1::flowRate=25.5
Pump1::pressure=5.2
Inverter1::frequency=50.0
Inverter1::voltage=380.0
```

## API 参考

### ConfigManager 主要接口
```cpp
// 设置参数
template<typename T>
void setParameter(const std::string& deviceName, const std::string& paramName, const T& value);

// 获取参数
template<typename T>
T getParameter(const std::string& deviceName, const std::string& paramName, const T& defaultValue = T{});

// 检查参数是否存在
bool hasParameter(const std::string& deviceName, const std::string& paramName) const;

// 加载/保存配置文件
bool loadFromFile(const std::string& filename = "device_config.txt");
bool saveToFile(const std::string& filename = "device_config.txt");
```

### BaseDevice 主要接口
```cpp
// 参数操作
template<typename T>
void setParameter(const std::string& paramName, const T& value);

template<typename T>
T getParameter(const std::string& paramName, const T& defaultValue = T{}) const;

// 设备控制
virtual void compute() = 0;  // 纯虚函数，子类实现
virtual bool initialize();   // 初始化
virtual void cleanup();      // 清理
```

### DeviceManager 主要接口
```cpp
// 设备管理
void addDevice(std::shared_ptr<BaseDevice> device);
std::shared_ptr<BaseDevice> findDevice(const std::string& deviceName);
bool removeDevice(const std::string& deviceName);

// 循环计算控制
void startComputeLoop(int intervalMs = 10);
void stopComputeLoop();
bool isComputeLoopRunning() const;

// 统计信息
ComputeStats getComputeStats() const;
```

## 性能特点

- **高效读写**: 基于std::unordered_map的O(1)平均时间复杂度
- **线程安全**: 使用std::mutex保护关键数据结构
- **实时同步**: 参数修改立即写入文件，无延迟
- **低开销**: 最小化锁持有时间，避免性能瓶颈

## 扩展开发

### 添加新设备类型

1. 继承BaseDevice基类
2. 实现compute()纯虚函数
3. 添加设备特有的参数和方法
4. 在构造函数中初始化默认参数

示例：
```cpp
class NewDevice : public BaseDevice {
public:
    explicit NewDevice(const std::string& deviceName) : BaseDevice(deviceName) {
        // 初始化默认参数
        if (!hasParameter("param1")) {
            setParameter("param1", defaultValue);
        }
    }
    
    void compute() override {
        // 实现设备特有的计算逻辑
    }
    
    // 添加设备特有的方法
    void setParam1(double value) { setParameter("param1", value); }
    double getParam1() const { return getParameter("param1", 0.0); }
};
```

## 注意事项

1. **线程安全**: 所有公共接口都是线程安全的，可以在多线程环境中使用
2. **文件权限**: 确保程序对配置文件目录有读写权限
3. **异常处理**: 建议在使用时添加适当的异常处理
4. **资源管理**: 使用智能指针管理设备对象，自动处理内存释放

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
