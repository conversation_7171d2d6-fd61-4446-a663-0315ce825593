#include "ConfigManager.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>
#include <random>

/**
 * 高效配置管理器测试程序
 * 测试优化后的异步批量写入功能
 */

void testBasicFunctionality() {
    std::cout << "\n=== 基本功能测试 ===" << std::endl;
    
    auto& config = ConfigManager::getInstance();
    
    // 测试不同数据类型
    config.setParameter("TestDevice", "intParam", 42);
    config.setParameter("TestDevice", "doubleParam", 3.14159);
    config.setParameter("TestDevice", "boolParam", true);
    config.setParameter("TestDevice", "stringParam", std::string("Hello World"));
    
    // 读取参数
    int intVal = config.getParameter("TestDevice", "intParam", 0);
    double doubleVal = config.getParameter("TestDevice", "doubleParam", 0.0);
    bool boolVal = config.getParameter("TestDevice", "boolParam", false);
    std::string stringVal = config.getParameter("TestDevice", "stringParam", std::string(""));
    
    std::cout << "Int参数: " << intVal << std::endl;
    std::cout << "Double参数: " << doubleVal << std::endl;
    std::cout << "Bool参数: " << (boolVal ? "true" : "false") << std::endl;
    std::cout << "String参数: " << stringVal << std::endl;
    
    // 测试参数存在性检查
    std::cout << "intParam存在: " << (config.hasParameter("TestDevice", "intParam") ? "是" : "否") << std::endl;
    std::cout << "nonExistParam存在: " << (config.hasParameter("TestDevice", "nonExistParam") ? "是" : "否") << std::endl;
}

void testPerformance() {
    std::cout << "\n=== 性能测试 ===" << std::endl;
    
    auto& config = ConfigManager::getInstance();
    
    // 设置较短的批量写入间隔以便测试
    config.setBatchWriteInterval(50); // 50ms
    
    const int numDevices = 10;
    const int numParams = 20;
    const int numIterations = 100;
    
    std::cout << "测试配置: " << numDevices << " 个设备, 每个设备 " << numParams 
              << " 个参数, " << numIterations << " 次迭代" << std::endl;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // 大量参数写入测试
    for (int iter = 0; iter < numIterations; iter++) {
        for (int device = 0; device < numDevices; device++) {
            std::string deviceName = "Device" + std::to_string(device);
            
            for (int param = 0; param < numParams; param++) {
                std::string paramName = "param" + std::to_string(param);
                double value = iter * 100.0 + device * 10.0 + param;
                
                config.setParameter(deviceName, paramName, value);
            }
        }
        
        // 每10次迭代输出一次进度
        if ((iter + 1) % 10 == 0) {
            std::cout << "完成 " << (iter + 1) << "/" << numIterations << " 次迭代" << std::endl;
        }
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double, std::milli>(endTime - startTime);
    
    int totalOperations = numDevices * numParams * numIterations;
    double opsPerSecond = totalOperations / (duration.count() / 1000.0);
    
    std::cout << "性能测试完成:" << std::endl;
    std::cout << "总操作数: " << totalOperations << std::endl;
    std::cout << "总耗时: " << duration.count() << " ms" << std::endl;
    std::cout << "平均每次操作: " << duration.count() / totalOperations << " ms" << std::endl;
    std::cout << "操作速度: " << opsPerSecond << " ops/sec" << std::endl;
    
    // 等待所有写入完成
    std::cout << "等待异步写入完成..." << std::endl;
    config.flushToDisk();
    
    // 显示性能统计
    auto stats = config.getPerformanceStats();
    std::cout << "\n写入统计:" << std::endl;
    std::cout << "总写入次数: " << stats.totalWrites << std::endl;
    std::cout << "批量写入次数: " << stats.batchWrites << std::endl;
    std::cout << "平均批量大小: " << stats.averageBatchSize << std::endl;
    std::cout << "总写入时间: " << stats.totalWriteTime << " ms" << std::endl;
    std::cout << "平均每次批量写入时间: " << (stats.batchWrites > 0 ? stats.totalWriteTime / stats.batchWrites : 0) << " ms" << std::endl;
}

void testConcurrency() {
    std::cout << "\n=== 并发测试 ===" << std::endl;
    
    auto& config = ConfigManager::getInstance();
    
    const int numThreads = 4;
    const int operationsPerThread = 500;
    
    std::cout << "启动 " << numThreads << " 个线程，每个线程执行 " << operationsPerThread << " 次操作" << std::endl;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    std::vector<std::thread> threads;
    
    for (int t = 0; t < numThreads; t++) {
        threads.emplace_back([&config, t, operationsPerThread]() {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_real_distribution<> dis(0.0, 1000.0);
            
            for (int i = 0; i < operationsPerThread; i++) {
                std::string deviceName = "ConcurrentDevice" + std::to_string(t);
                std::string paramName = "param" + std::to_string(i % 10);
                double value = dis(gen);
                
                config.setParameter(deviceName, paramName, value);
                
                // 偶尔读取参数
                if (i % 10 == 0) {
                    double readValue = config.getParameter(deviceName, paramName, 0.0);
                    (void)readValue; // 避免未使用变量警告
                }
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double, std::milli>(endTime - startTime);
    
    int totalOperations = numThreads * operationsPerThread;
    double opsPerSecond = totalOperations / (duration.count() / 1000.0);
    
    std::cout << "并发测试完成:" << std::endl;
    std::cout << "总操作数: " << totalOperations << std::endl;
    std::cout << "总耗时: " << duration.count() << " ms" << std::endl;
    std::cout << "操作速度: " << opsPerSecond << " ops/sec" << std::endl;
    
    // 等待所有写入完成
    config.flushToDisk();
}

void testConfigPersistence() {
    std::cout << "\n=== 配置持久化测试 ===" << std::endl;
    
    auto& config = ConfigManager::getInstance();
    
    // 设置一些测试参数
    config.setParameter("PersistDevice", "param1", 123.45);
    config.setParameter("PersistDevice", "param2", true);
    config.setParameter("PersistDevice", "param3", std::string("test_value"));
    
    std::cout << "设置参数完成，等待写入..." << std::endl;
    config.flushToDisk();
    
    // 读取参数验证
    double param1 = config.getParameter("PersistDevice", "param1", 0.0);
    bool param2 = config.getParameter("PersistDevice", "param2", false);
    std::string param3 = config.getParameter("PersistDevice", "param3", std::string(""));
    
    std::cout << "从内存读取:" << std::endl;
    std::cout << "param1: " << param1 << std::endl;
    std::cout << "param2: " << (param2 ? "true" : "false") << std::endl;
    std::cout << "param3: " << param3 << std::endl;
    
    // 重新加载配置文件
    std::cout << "重新加载配置文件..." << std::endl;
    config.loadFromFile();
    
    // 再次读取参数验证持久化
    param1 = config.getParameter("PersistDevice", "param1", 0.0);
    param2 = config.getParameter("PersistDevice", "param2", false);
    param3 = config.getParameter("PersistDevice", "param3", std::string(""));
    
    std::cout << "从文件重新加载后:" << std::endl;
    std::cout << "param1: " << param1 << std::endl;
    std::cout << "param2: " << (param2 ? "true" : "false") << std::endl;
    std::cout << "param3: " << param3 << std::endl;
}

int main() {
    std::cout << "=== 高效配置管理器测试程序 ===" << std::endl;
    
    try {
        testBasicFunctionality();
        testPerformance();
        testConcurrency();
        testConfigPersistence();
        
        std::cout << "\n=== 所有测试完成 ===" << std::endl;
        
        // 最终统计
        auto& config = ConfigManager::getInstance();
        auto stats = config.getPerformanceStats();
        std::cout << "\n最终性能统计:" << std::endl;
        std::cout << "总写入次数: " << stats.totalWrites << std::endl;
        std::cout << "批量写入次数: " << stats.batchWrites << std::endl;
        std::cout << "平均批量大小: " << stats.averageBatchSize << std::endl;
        std::cout << "总写入时间: " << stats.totalWriteTime << " ms" << std::endl;
        
        if (stats.batchWrites > 0) {
            std::cout << "平均每次批量写入时间: " << stats.totalWriteTime / stats.batchWrites << " ms" << std::endl;
            std::cout << "批量写入效率提升: " << (stats.totalWrites / stats.batchWrites) << "x" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
