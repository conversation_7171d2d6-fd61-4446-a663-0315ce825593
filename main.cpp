#include "DeviceManager.h"
#include "Motor.h"
#include "Pump.h"
#include "Inverter.h"
#include <iostream>
#include <thread>
#include <chrono>

void printMenu() {
    std::cout << "\n========== Device Control System ==========" << std::endl;
    std::cout << "1. Add Motor" << std::endl;
    std::cout << "2. Add Pump" << std::endl;
    std::cout << "3. Add Inverter" << std::endl;
    std::cout << "4. List All Devices" << std::endl;
    std::cout << "5. Control Motor" << std::endl;
    std::cout << "6. Control Pump" << std::endl;
    std::cout << "7. Control Inverter" << std::endl;
    std::cout << "8. Start Compute Loop" << std::endl;
    std::cout << "9. Stop Compute Loop" << std::endl;
    std::cout << "10. Show Compute Statistics" << std::endl;
    std::cout << "11. Print All Device Status" << std::endl;
    std::cout << "12. Save Configuration" << std::endl;
    std::cout << "13. Load Configuration" << std::endl;
    std::cout << "0. Exit" << std::endl;
    std::cout << "===========================================" << std::endl;
    std::cout << "Enter your choice: ";
}

void controlMotor(const std::string& motorName) {
    auto& manager = DeviceManager::getInstance();
    auto device = manager.findDevice(motorName);
    
    if (!device) {
        std::cout << "Motor '" << motorName << "' not found!" << std::endl;
        return;
    }
    
    auto motor = std::dynamic_pointer_cast<Motor>(device);
    if (!motor) {
        std::cout << "Device '" << motorName << "' is not a motor!" << std::endl;
        return;
    }
    
    int choice;
    std::cout << "\n--- Motor Control: " << motorName << " ---" << std::endl;
    std::cout << "1. Set Speed  2. Set Direction  3. Enable/Disable  4. Set Max Speed  5. Show Status" << std::endl;
    std::cout << "Choice: ";
    std::cin >> choice;
    
    switch (choice) {
        case 1: {
            double speed;
            std::cout << "Enter speed: ";
            std::cin >> speed;
            motor->setSpeed(speed);
            break;
        }
        case 2: {
            int dir;
            std::cout << "Enter direction (1=clockwise, 0=counter-clockwise): ";
            std::cin >> dir;
            motor->setDirection(dir == 1);
            break;
        }
        case 3: {
            int enabled;
            std::cout << "Enable motor? (1=yes, 0=no): ";
            std::cin >> enabled;
            motor->setEnabled(enabled == 1);
            break;
        }
        case 4: {
            double maxSpeed;
            std::cout << "Enter max speed: ";
            std::cin >> maxSpeed;
            motor->setMaxSpeed(maxSpeed);
            break;
        }
        case 5:
            motor->printStatus();
            break;
        default:
            std::cout << "Invalid choice!" << std::endl;
    }
}

void controlPump(const std::string& pumpName) {
    auto& manager = DeviceManager::getInstance();
    auto device = manager.findDevice(pumpName);
    
    if (!device) {
        std::cout << "Pump '" << pumpName << "' not found!" << std::endl;
        return;
    }
    
    auto pump = std::dynamic_pointer_cast<Pump>(device);
    if (!pump) {
        std::cout << "Device '" << pumpName << "' is not a pump!" << std::endl;
        return;
    }
    
    int choice;
    std::cout << "\n--- Pump Control: " << pumpName << " ---" << std::endl;
    std::cout << "1. Set Flow Rate  2. Set Pressure  3. Enable/Disable  4. Set Max Flow Rate  5. Show Status" << std::endl;
    std::cout << "Choice: ";
    std::cin >> choice;
    
    switch (choice) {
        case 1: {
            double flowRate;
            std::cout << "Enter flow rate (L/min): ";
            std::cin >> flowRate;
            pump->setFlowRate(flowRate);
            break;
        }
        case 2: {
            double pressure;
            std::cout << "Enter pressure (bar): ";
            std::cin >> pressure;
            pump->setPressure(pressure);
            break;
        }
        case 3: {
            int enabled;
            std::cout << "Enable pump? (1=yes, 0=no): ";
            std::cin >> enabled;
            pump->setEnabled(enabled == 1);
            break;
        }
        case 4: {
            double maxFlowRate;
            std::cout << "Enter max flow rate (L/min): ";
            std::cin >> maxFlowRate;
            pump->setMaxFlowRate(maxFlowRate);
            break;
        }
        case 5:
            pump->printStatus();
            break;
        default:
            std::cout << "Invalid choice!" << std::endl;
    }
}

void controlInverter(const std::string& inverterName) {
    auto& manager = DeviceManager::getInstance();
    auto device = manager.findDevice(inverterName);
    
    if (!device) {
        std::cout << "Inverter '" << inverterName << "' not found!" << std::endl;
        return;
    }
    
    auto inverter = std::dynamic_pointer_cast<Inverter>(device);
    if (!inverter) {
        std::cout << "Device '" << inverterName << "' is not an inverter!" << std::endl;
        return;
    }
    
    int choice;
    std::cout << "\n--- Inverter Control: " << inverterName << " ---" << std::endl;
    std::cout << "1. Set Frequency  2. Set Voltage  3. Set Current  4. Enable/Disable  5. Show Status" << std::endl;
    std::cout << "Choice: ";
    std::cin >> choice;
    
    switch (choice) {
        case 1: {
            double frequency;
            std::cout << "Enter frequency (Hz): ";
            std::cin >> frequency;
            inverter->setFrequency(frequency);
            break;
        }
        case 2: {
            double voltage;
            std::cout << "Enter voltage (V): ";
            std::cin >> voltage;
            inverter->setVoltage(voltage);
            break;
        }
        case 3: {
            double current;
            std::cout << "Enter current (A): ";
            std::cin >> current;
            inverter->setCurrent(current);
            break;
        }
        case 4: {
            int enabled;
            std::cout << "Enable inverter? (1=yes, 0=no): ";
            std::cin >> enabled;
            inverter->setEnabled(enabled == 1);
            break;
        }
        case 5:
            inverter->printStatus();
            break;
        default:
            std::cout << "Invalid choice!" << std::endl;
    }
}

int main() {
    std::cout << "=== Device Configuration System Started ===" << std::endl;
    
    auto& manager = DeviceManager::getInstance();
    auto& config = ConfigManager::getInstance();
    
    // 加载配置文件
    config.loadFromFile();
    
    int choice;
    std::string deviceName;
    
    while (true) {
        printMenu();
        std::cin >> choice;
        
        switch (choice) {
            case 1: {
                std::cout << "Enter motor name: ";
                std::cin >> deviceName;
                auto motor = std::make_shared<Motor>(deviceName);
                motor->initialize();
                manager.addDevice(motor);
                break;
            }
            case 2: {
                std::cout << "Enter pump name: ";
                std::cin >> deviceName;
                auto pump = std::make_shared<Pump>(deviceName);
                pump->initialize();
                manager.addDevice(pump);
                break;
            }
            case 3: {
                std::cout << "Enter inverter name: ";
                std::cin >> deviceName;
                auto inverter = std::make_shared<Inverter>(deviceName);
                inverter->initialize();
                manager.addDevice(inverter);
                break;
            }
            case 4: {
                auto devices = manager.getAllDevices();
                std::cout << "\n--- Device List ---" << std::endl;
                for (const auto& device : devices) {
                    std::cout << "- " << device->getName() << std::endl;
                }
                std::cout << "Total: " << devices.size() << " devices" << std::endl;
                break;
            }
            case 5: {
                std::cout << "Enter motor name: ";
                std::cin >> deviceName;
                controlMotor(deviceName);
                break;
            }
            case 6: {
                std::cout << "Enter pump name: ";
                std::cin >> deviceName;
                controlPump(deviceName);
                break;
            }
            case 7: {
                std::cout << "Enter inverter name: ";
                std::cin >> deviceName;
                controlInverter(deviceName);
                break;
            }
            case 8: {
                int interval;
                std::cout << "Enter compute interval (ms, default 10): ";
                std::cin >> interval;
                manager.startComputeLoop(interval);
                break;
            }
            case 9:
                manager.stopComputeLoop();
                break;
            case 10: {
                auto stats = manager.getComputeStats();
                std::cout << "\n--- Compute Statistics ---" << std::endl;
                std::cout << "Total Cycles: " << stats.totalCycles << std::endl;
                std::cout << "Average Compute Time: " << stats.averageComputeTime << " ms" << std::endl;
                std::cout << "Max Compute Time: " << stats.maxComputeTime << " ms" << std::endl;
                std::cout << "Min Compute Time: " << stats.minComputeTime << " ms" << std::endl;
                break;
            }
            case 11:
                manager.printAllDevicesStatus();
                break;
            case 12:
                config.saveToFile();
                std::cout << "Configuration saved!" << std::endl;
                break;
            case 13:
                config.loadFromFile();
                std::cout << "Configuration loaded!" << std::endl;
                break;
            case 0:
                std::cout << "Shutting down system..." << std::endl;
                manager.stopComputeLoop();
                config.saveToFile();
                return 0;
            default:
                std::cout << "Invalid choice! Please try again." << std::endl;
        }
    }
    
    return 0;
}
