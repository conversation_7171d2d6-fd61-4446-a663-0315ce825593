#include "DeviceManager.h"
#include "Motor.h"
#include "Pump.h"
#include "Inverter.h"
#include <iostream>
#include <thread>
#include <chrono>

/**
 * 简单的测试程序，演示系统的基本功能
 * 这个程序会自动创建一些设备，设置参数，启动计算循环，
 * 然后动态修改参数来演示实时配置功能
 */
void runAutomaticTest() {
    std::cout << "=== 自动测试程序开始 ===" << std::endl;
    
    auto& manager = DeviceManager::getInstance();
    auto& config = ConfigManager::getInstance();
    
    // 1. 创建设备
    std::cout << "\n1. 创建设备..." << std::endl;
    auto motor1 = std::make_shared<Motor>("Motor1");
    auto motor2 = std::make_shared<Motor>("Motor2");
    auto pump1 = std::make_shared<Pump>("Pump1");
    auto inverter1 = std::make_shared<Inverter>("Inverter1");
    
    // 2. 初始化设备
    std::cout << "\n2. 初始化设备..." << std::endl;
    motor1->initialize();
    motor2->initialize();
    pump1->initialize();
    inverter1->initialize();
    
    // 3. 添加到管理器
    std::cout << "\n3. 添加设备到管理器..." << std::endl;
    manager.addDevice(motor1);
    manager.addDevice(motor2);
    manager.addDevice(pump1);
    manager.addDevice(inverter1);
    
    // 4. 设置初始参数
    std::cout << "\n4. 设置初始参数..." << std::endl;
    motor1->setSpeed(50.0);
    motor1->setMaxSpeed(500.0);
    motor1->setDirection(true);
    motor1->setEnabled(true);
    
    motor2->setSpeed(75.0);
    motor2->setMaxSpeed(800.0);
    motor2->setDirection(false);
    motor2->setEnabled(true);
    
    pump1->setFlowRate(30.0);
    pump1->setPressure(6.5);
    pump1->setMaxFlowRate(100.0);
    pump1->setEnabled(true);
    
    inverter1->setFrequency(50.0);
    inverter1->setVoltage(380.0);
    inverter1->setCurrent(10.0);
    inverter1->setEnabled(true);
    
    // 5. 显示初始状态
    std::cout << "\n5. 显示初始设备状态..." << std::endl;
    motor1->printStatus();
    motor2->printStatus();
    pump1->printStatus();
    inverter1->printStatus();
    
    // 6. 启动计算循环
    std::cout << "\n6. 启动计算循环 (10ms间隔)..." << std::endl;
    manager.startComputeLoop(10);
    
    // 7. 运行一段时间并动态修改参数
    std::cout << "\n7. 系统运行中，动态修改参数..." << std::endl;
    
    for (int i = 0; i < 10; i++) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 动态修改参数
        double newSpeed = 50.0 + i * 10.0;
        motor1->setSpeed(newSpeed);
        
        double newFlowRate = 30.0 + i * 5.0;
        pump1->setFlowRate(newFlowRate);
        
        double newFrequency = 50.0 + i * 2.0;
        inverter1->setFrequency(newFrequency);
        
        std::cout << "第 " << (i+1) << " 秒: Motor1速度=" << newSpeed 
                  << ", Pump1流量=" << newFlowRate 
                  << ", Inverter1频率=" << newFrequency << std::endl;
    }
    
    // 8. 显示计算统计
    std::cout << "\n8. 显示计算统计信息..." << std::endl;
    auto stats = manager.getComputeStats();
    std::cout << "总计算周期: " << stats.totalCycles << std::endl;
    std::cout << "平均计算时间: " << stats.averageComputeTime << " ms" << std::endl;
    std::cout << "最大计算时间: " << stats.maxComputeTime << " ms" << std::endl;
    std::cout << "最小计算时间: " << stats.minComputeTime << " ms" << std::endl;
    
    // 9. 显示最终状态
    std::cout << "\n9. 显示最终设备状态..." << std::endl;
    manager.printAllDevicesStatus();
    
    // 10. 停止计算循环
    std::cout << "\n10. 停止计算循环..." << std::endl;
    manager.stopComputeLoop();
    
    // 11. 保存配置
    std::cout << "\n11. 保存配置到文件..." << std::endl;
    config.saveToFile("test_config.txt");
    
    // 12. 测试配置加载
    std::cout << "\n12. 测试配置文件加载..." << std::endl;
    
    // 修改一些参数
    motor1->setSpeed(999.0);
    pump1->setFlowRate(999.0);
    
    std::cout << "修改后 - Motor1速度: " << motor1->getSpeed() << std::endl;
    std::cout << "修改后 - Pump1流量: " << pump1->getFlowRate() << std::endl;
    
    // 重新加载配置
    config.loadFromFile("test_config.txt");
    
    std::cout << "重新加载后 - Motor1速度: " << motor1->getSpeed() << std::endl;
    std::cout << "重新加载后 - Pump1流量: " << pump1->getFlowRate() << std::endl;
    
    std::cout << "\n=== 自动测试程序完成 ===" << std::endl;
}

/**
 * 测试配置管理器的基本功能
 */
void testConfigManager() {
    std::cout << "\n=== 测试配置管理器 ===" << std::endl;
    
    auto& config = ConfigManager::getInstance();
    
    // 测试不同数据类型
    config.setParameter("TestDevice", "intParam", 42);
    config.setParameter("TestDevice", "doubleParam", 3.14159);
    config.setParameter("TestDevice", "boolParam", true);
    config.setParameter("TestDevice", "stringParam", std::string("Hello World"));
    
    // 读取参数
    int intVal = config.getParameter("TestDevice", "intParam", 0);
    double doubleVal = config.getParameter("TestDevice", "doubleParam", 0.0);
    bool boolVal = config.getParameter("TestDevice", "boolParam", false);
    std::string stringVal = config.getParameter("TestDevice", "stringParam", std::string(""));
    
    std::cout << "Int参数: " << intVal << std::endl;
    std::cout << "Double参数: " << doubleVal << std::endl;
    std::cout << "Bool参数: " << (boolVal ? "true" : "false") << std::endl;
    std::cout << "String参数: " << stringVal << std::endl;
    
    // 测试参数存在性检查
    std::cout << "intParam存在: " << (config.hasParameter("TestDevice", "intParam") ? "是" : "否") << std::endl;
    std::cout << "nonExistParam存在: " << (config.hasParameter("TestDevice", "nonExistParam") ? "是" : "否") << std::endl;
    
    // 获取设备所有参数
    auto params = config.getDeviceParameters("TestDevice");
    std::cout << "TestDevice的所有参数:" << std::endl;
    for (const auto& param : params) {
        std::cout << "  " << param.first << " = " << param.second << std::endl;
    }
}

int main() {
    std::cout << "选择测试模式:" << std::endl;
    std::cout << "1. 自动测试程序" << std::endl;
    std::cout << "2. 配置管理器测试" << std::endl;
    std::cout << "3. 交互式主程序" << std::endl;
    std::cout << "请输入选择 (1-3): ";
    
    int choice;
    std::cin >> choice;
    
    switch (choice) {
        case 1:
            runAutomaticTest();
            break;
        case 2:
            testConfigManager();
            break;
        case 3:
            // 这里可以调用原来的main函数逻辑
            std::cout << "启动交互式程序..." << std::endl;
            // 可以把原来main.cpp的逻辑移到这里，或者创建另一个函数
            break;
        default:
            std::cout << "无效选择!" << std::endl;
            return 1;
    }
    
    return 0;
}
